<template>
  <div>
    <v-container fluid>
      <v-row>
        <v-col cols="12">
          <v-card>
            <v-card-title class="d-flex align-center">
              <v-icon class="mr-2">mdi-file-excel</v-icon>
              Excel Upload - Shipping Data
            </v-card-title>
            <v-card-text>
              <!-- Upload Section -->
              <v-row>
                <v-col cols="12" md="6">
                  <v-card variant="outlined" class="pa-4">
                    <v-card-title class="text-h6 mb-4">
                      Upload Excel File
                    </v-card-title>
                    <v-file-input
                      v-model="selectedFile"
                      accept=".xlsx,.xls"
                      label="Select Excel File"
                      prepend-icon="mdi-file-excel"
                      :rules="fileRules"
                      show-size
                      counter
                    ></v-file-input>
                    <v-alert
                      v-if="fileInfo"
                      type="info"
                      variant="tonal"
                      class="mt-3"
                    >
                      <div><strong>File:</strong> {{ fileInfo.name }}</div>
                      <div><strong>Size:</strong> {{ formatFileSize(fileInfo.size) }}</div>
                      <div><strong>Type:</strong> {{ fileInfo.type }}</div>
                    </v-alert>
                    <v-btn
                      color="primary"
                      :loading="uploading"
                      :disabled="!selectedFile || uploading"
                      @click="uploadFile"
                      class="mt-4"
                    >
                      <v-icon left>mdi-upload</v-icon>
                      Upload Excel
                    </v-btn>
                  </v-card>
                </v-col>
              </v-row>
              <!-- Upload Progress -->
              <v-row v-if="uploading">
                <v-col cols="12">
                  <v-card variant="outlined" class="pa-4">
                    <v-card-title class="text-h6">
                      Uploading...
                    </v-card-title>
                    <v-progress-linear
                      v-model="uploadProgress"
                      color="primary"
                      height="20"
                      rounded
                    >
                      <template v-slot:default="{ value }">
                        <strong>{{ Math.ceil(value) }}%</strong>
                      </template>
                    </v-progress-linear>
                  </v-card>
                </v-col>
              </v-row>
              <!-- Results Section -->
              <v-row v-if="apiMessage">
                <v-col cols="12">
                  <v-card variant="outlined" class="pa-4">
                    <v-card-title class="d-flex align-center justify-space-between">
                      <span class="text-h6">
                        <v-icon class="mr-2" color="success">mdi-check-circle</v-icon>
                        Upload Result
                      </span>
                    </v-card-title>
                    <v-alert
                      type="info"
                      variant="tonal"
                      class="mb-4"
                    >
                      {{ apiMessage }}
                      <template v-if="updatedCount !== null">
                        <br>
                        <strong>Updated:</strong> {{ updatedCount }}
                      </template>
                    </v-alert>
                    <!-- Effected Data Table -->
                    <div v-if="effectedData.length > 0">
                      <v-data-table
                        :headers="effectedHeaders"
                        :items="effectedData"
                        density="compact"
                        class="elevation-1"
                      />
                    </div>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script setup lang="ts">
useHead({
  title: "Excel Upload - Shipping Data",
});

definePageMeta({
  middleware: "auth",
});

// Import components
import { ref, computed } from 'vue';

// Reactive data
const selectedFile = ref<File | null>(null);
const uploading = ref(false);
const uploadProgress = ref(0);
const effectedData = ref<any[]>([]);
const apiMessage = ref('');
const updatedCount = ref<number | null>(null);

// File validation rules
const fileRules = [
  (value: File | null) => {
    if (!value) return 'File is required';
    if (!value.name.match(/\.(xlsx|xls)$/)) return 'File must be an Excel file (.xlsx or .xls)';
    if (value.size > 10 * 1024 * 1024) return 'File size must be less than 10MB';
    return true;
  }
];

// Table headers for effected_data
const effectedHeaders = [
  { title: 'DocNum', key: 'header_docnum' },
  { title: 'Cargo', key: 'Cargo' },
  { title: 'Voyage', key: 'Voyage' },
  { title: 'Shipment', key: 'Shipement' },
  { title: 'No B/L', key: 'No_bl' },
  { title: 'Date B/L', key: 'date_bl' },
  { title: 'No Inv', key: 'No_inv' },
  { title: 'No Container', key: 'ContainerNo' },
  { title: 'Tenant', key: 'tenant_name' }
];

const fileInfo = computed(() => {
  if (!selectedFile.value) return null;
  return {
    name: selectedFile.value.name,
    size: selectedFile.value.size,
    type: selectedFile.value.type
  };
});

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const uploadFile = async () => {
  if (!selectedFile.value) return;

  uploading.value = true;
  uploadProgress.value = 0;
  apiMessage.value = '';
  updatedCount.value = null;
  effectedData.value = [];

  try {
    const formData = new FormData();
    formData.append('file', selectedFile.value);

    // Simulate upload progress
    let progressInterval: ReturnType<typeof setInterval> | null = null;
    progressInterval = setInterval(() => {
      if (uploadProgress.value < 90) {
        uploadProgress.value += Math.random() * 10;
      }
    }, 200);

    // Make API call
    const response: any = await useMyFetch('/api/transact/import-inv/upload-excel', {
      method: 'POST',
      body: formData,
      onUploadProgress: (progressEvent: ProgressEvent) => {
        if (progressEvent.total) {
          uploadProgress.value = (progressEvent.loaded / progressEvent.total) * 100;
        }
      }
    });

    if (progressInterval) clearInterval(progressInterval as any);
    uploadProgress.value = 100;

    // Process response
    if (response.success) {
      apiMessage.value = response.message || 'Upload successful!';
      updatedCount.value = response.updated ?? null;
      effectedData.value = response.effected_data || [];
    } else {
      apiMessage.value = response.message || 'Upload failed.';
      updatedCount.value = null;
      effectedData.value = [];
    }

    // Reset file selection
    selectedFile.value = null;

  } catch (error: any) {
    apiMessage.value = error.message || 'Upload failed. Please try again.';
    updatedCount.value = null;
    effectedData.value = [];
  } finally {
    uploading.value = false;
    uploadProgress.value = 0;
  }
};
</script>

<style scoped>
.v-data-table {
  border-radius: 8px;
}

.v-card {
  border-radius: 12px;
}
</style>
